﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>c33c1427-6955-4bc9-8680-102593e93ce9</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.ApiEndpoints" Version="4.1.0" />
    <PackageReference Include="Ardalis.ApiEndpoints.Swashbuckle" Version="4.1.0" />
    <PackageReference Include="AspNetCore.HealthChecks.AzureApplicationInsights" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.AzureServiceBus" Version="9.0.0" />
    <PackageReference Include="ChristopherBriddock.AspNetCore.Extensions" Version="9.0.1" />
    <PackageReference Include="ChristopherBriddock.AspNetCore.HealthChecks" Version="9.0.4" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Fido2" Version="3.0.1" />
    <PackageReference Include="Fido2.AspNet" Version="3.0.1" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageReference Include="Konscious.Security.Cryptography.Argon2" Version="1.3.1" />
    <PackageReference Include="MassTransit.Azure.ServiceBus.Core" Version="8.4.0" />
    <PackageReference Include="MassTransit.EntityFrameworkCore" Version="8.4.0" />
    <PackageReference Include="MassTransit.RabbitMQ" Version="8.4.0" />
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.23.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.Kubernetes" Version="7.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Caching.Hybrid" Version="9.6.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.6" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.12.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.6" />
    <PackageReference Include="UniqueIdentifier" Version="1.0.0" />
    <PackageReference Include="ZiggyCreatures.FusionCache" Version="2.3.0" />
    <PackageReference Include="ZiggyCreatures.FusionCache.Backplane.StackExchangeRedis" Version="2.3.0" />
    <PackageReference Include="ZiggyCreatures.FusionCache.OpenTelemetry" Version="2.3.0" />
    <PackageReference Include="ZiggyCreatures.FusionCache.Serialization.SystemTextJson" Version="2.3.0" />
  </ItemGroup>

  <ItemGroup>
  <Content Update="appsettings.Development.json">
    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
  </Content>
  <Content Update="appsettings.json">
    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
  </Content>
</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Authentica.Common\Authentica.Common.csproj" />
  </ItemGroup>

</Project>
